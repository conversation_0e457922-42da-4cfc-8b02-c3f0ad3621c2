#!/usr/bin/env python3
"""
Script to convert HTML files to MJML format using the parser functions.
"""

import sys
import os
from bs4 import BeautifulSoup, Tag
import re

# Import the functions from parser.py
def parse_inline_styles(style_string):
    """
    Parses an inline style string and returns a dictionary of CSS properties.
    """
    styles = {}
    if style_string:
        for declaration in style_string.split(';'):
            if ':' in declaration:
                prop, value = declaration.split(':', 1)
                styles[prop.strip()] = value.strip()
    return styles

def map_styles_to_mjml_attrs(styles):
    """
    Maps CSS properties from a style dictionary to MJML attributes.
    Returns two dictionaries: one for direct MJML attributes and one for content styles.
    """
    mjml_attrs = {}
    content_styles = {} # Styles that might apply to the inner mj-text content

    style_map = {
        'background-color': 'background-color',
        'color': 'color',
        'font-family': 'font-family',
        'font-size': 'font-size',
        'font-weight': 'font-weight',
        'line-height': 'line-height',
        'text-align': 'align', # MJML uses 'align' for text alignment
        'padding': 'padding',
        'padding-top': 'padding-top',
        'padding-bottom': 'padding-bottom',
        'padding-left': 'padding-left',
        'padding-right': 'padding-right',
        'border-radius': 'border-radius',
        'width': 'width',
        'height': 'height',
        'vertical-align': 'vertical-align', # For td, maps to mj-column
        'text-transform': 'text-transform',
        'text-decoration': 'text-decoration',
        'letter-spacing': 'letter-spacing',
    }

    # Iterate through common style properties and map them
    for css_prop, mjml_attr in style_map.items():
        if css_prop in styles:
            mjml_attrs[mjml_attr] = styles[css_prop]

    # Handle margin specifically for mj-text, mj-image etc.
    if 'margin' in styles:
        content_styles['margin'] = styles['margin']
    if 'margin-top' in styles:
        content_styles['margin-top'] = styles['margin-top']
    if 'margin-bottom' in styles:
        content_styles['margin-bottom'] = styles['margin-bottom']
    if 'margin-left' in styles:
        content_styles['margin-left'] = styles['margin-left']
    if 'margin-right' in styles:
        content_styles['margin-right'] = styles['margin-right']

    # Special handling for background on elements that can take it
    if 'background' in styles:
        mjml_attrs['background-url'] = re.search(r'url\((.*?)\)', styles['background'])
        if mjml_attrs['background-url']:
            mjml_attrs['background-url'] = mjml_attrs['background-url'].group(1).strip("'\"")
            del styles['background'] # Remove if handled by background-url
        if 'background-color' not in mjml_attrs:
            # Try to extract color if not already set
            color_match = re.search(r'#[0-9a-fA-F]{3,6}|\b(?:rgb|rgba|hsl|hsla)\([^)]+\)|\b[a-zA-Z]+\b', styles['background'])
            if color_match:
                mjml_attrs['background-color'] = color_match.group(0)

    return mjml_attrs, content_styles

def convert_html_file_to_mjml(html_file_path):
    """
    Convert HTML file to MJML markup using the parser functions.
    """
    with open(html_file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Use the html_to_mjml_xml function from parser.py
    from parser import html_to_mjml_xml
    
    return html_to_mjml_xml(html_content)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python convert_html_to_mjml.py <html_file>")
        print("Example: python convert_html_to_mjml.py Example_case_1_trg_9010_early_upsell_chk.html")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    if not os.path.exists(html_file):
        print(f"Error: File {html_file} not found")
        sys.exit(1)
    
    try:
        mjml_output = convert_html_file_to_mjml(html_file)
        
        # Output to .mjml file
        base_name = os.path.splitext(html_file)[0]
        output_file = f"{base_name}.mjml"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mjml_output)
        
        print(f"✅ Successfully converted {html_file} to {output_file}")
        print(f"📧 You can now run: mjml {output_file} -o {base_name}_final.html")
        
    except Exception as e:
        print(f"❌ Error converting file: {e}")
        sys.exit(1)
