<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd"><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"></td></tr></table>


<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <style type="text/css">
      ReadMsgBody{ width: 100%;}
      .ExternalClass {width: 100%;}
      .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {line-height: 100%;}
      body {-webkit-text-size-adjust:100%; -ms-text-size-adjust:100%;margin:0 !important;}
      p { margin: 1em 0;}
      table td { border-collapse: collapse;}
      img {outline:0;}
      a img {border:none;}
      @-ms-viewport{ width: device-width;}
    </style>
    <style type="text/css">
      @media only screen and (max-width: 480px) {
        .container {width: 100% !important;}
        .footer { width:auto !important; margin-left:0; }
        .mobile-hidden { display:none !important; }
        .logo { display:block !important; padding:0 !important; }
        img { max-width:100% !important; height:auto !important; max-height:auto !important;}
        .header img{max-width:100% !important;height:auto !important; max-height:auto !important;}
        .photo img { width:100% !important; max-width:100% !important; height:auto !important;}
        .drop { display:block !important; width: 100% !important; float:left; clear:both;}
        .footerlogo { display:block !important; width: 100% !important; padding-top:15px; float:left; clear:both;}
        .nav4, .nav5, .nav6 { display: none !important; }
        .tableBlock {width:100% !important;}
        .responsive-td {width:100% !important; display:block !important; padding:0 !important; }
        .fluid, .fluid-centered {
          width: 100% !important;
          max-width: 100% !important;
          height: auto !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }
        .fluid-centered {
          margin-left: auto !important;
          margin-right: auto !important;
        }
        /* MOBILE GLOBAL STYLES - DO NOT CHANGE */
body, .tb_properties{font-family: Arial !important; font-size: 16px !important; color: #808080 !important; line-height: 1.5 !important; padding: 0px !important; }.buttonstyles{font-family: Arial, helvetica, sans-serif !important; font-size: 16px !important; color: #FFFFFF !important; padding: 0px !important; }h1{font-family: Arial !important; font-size: 22px !important; color: #202020 !important; line-height: 1 !important; }h2{font-family: Arial !important; font-size: 20px !important; color: #202020 !important; line-height: 1 !important; }h3{font-family: Arial !important; font-size: 18px !important; color: #202020 !important; line-height: 1 !important; }a:not(.buttonstyles){line-height: 1
 !important; }.mobile-hidden{display: none !important; }.responsive-td {width: 100% !important; display: block !important; padding: 0 !important;}
/* END OF MOBILE GLOBAL STYLES - DO NOT CHANGE */
      }
      @media only screen and (max-width: 640px) {
        .container { width:100% !important; }
        .mobile-hidden { display:none !important; }
        .logo { display:block !important; padding:0 !important; }
        .photo img { width:100% !important; height:auto !important;}
        .nav5, .nav6 { display: none !important;}
        .fluid, .fluid-centered {
          width: 100% !important;
          max-width: 100% !important;
          height: auto !important;
          margin-left: auto !important;
          margin-right: auto !important;
        }
        .fluid-centered {
          margin-left: auto !important;
          margin-right: auto !important;
        }
      }
    </style>
    <!--[if mso]>       <style type="text/css">           /* Begin Outlook Font Fix */           body, table, td {               font-family: Arial, Helvetica, sans-serif ;               font-size:16px;               color:#000000;               line-height:1;           }           /* End Outlook Font Fix */       </style>     <![endif]-->
  </head>
  <body bgcolor="#ffffff" text="#000000" style="background-color: #ffffff; color: #000000; padding: 0px; -webkit-text-size-adjust:none; font-size: 16px; font-family:arial,helvetica,sans-serif;"><style type="text/css">
div.preheader 
{ display: none !important; } 
</style>
<div class="preheader" style="font-size: 1px; display: none !important;">Access special features by connecting an Ally Bank Spending Account to your Savings Account.</div>

    <table width="100%" border="0" cellpadding="0" cellspacing="0" align="center">      
      <tr>
        <td align="center">
          <table cellspacing="0" cellpadding="0" border="0" width="736" class="container" align="center">
            <tr>
              <td>
                <table class="tb_properties border_style" style="background-color:#FFFFFF;" cellspacing="0" cellpadding="0" bgcolor="#ffffff" width="100%">
                  <tr>
                    <td align="center" valign="top">
                      <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                          <!-- added padding here -->
                          <td class="content_padding" style="">
                            <!-- end of comment -->
                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                              <tr> <!-- top slot -->
                                <td align="center" class="header" valign="top">
                                  <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tbody>
                                        <tr>
                                          <td align="left" valign="top">
                                            <table cellspacing="0" cellpadding="0" style="width:100%">
                                              <tbody>
                                              <tr>
                                                <td class="responsive-td" valign="top" style="width: 100%;">
                                                  <table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #300942; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        

       <!--[if gte mso 9]>             <xml>                 <o:OfficeDocumentSettings>                     <o:AllowPNG/>                     <o:PixelsPerInch>96</o:PixelsPerInch>                 </o:OfficeDocumentSettings>             </xml>        <![endif]-->

       <!--[if gt mso 15]>            <style>                  table {                       border-collapse: collapse;                   }                   .TopBar_logoLitany img {                      padding:4px 0px 0px 5px !important;                   }            </style>         <![endif]-->

        <style type="text/css">
            @media screen and (max-width:480px) {
                .TopBar_0 {
                    max-width: 0px !important;
                    width: 0px !important;
                }
                .TopBar {
                    padding:0px 10px 0px 20px !important;
                }
                .TopBar_logoAlly img {
                    transform: scale(.8) !important;
                    text-align: left !important;
                    max-width: 100% !important;
                }
                .TopBar_logoLitany img {
                    transform: scale(.77) !important;
                    text-align: right !important;
                    max-width: 100% !important;
                    padding:0px 0px 2px 0px !important;
                }
            }
       </style>
    </head>  
  </html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">            
<tr>
             <td height="25" style="height: 25px; line-height: 25px; max-height: 25px">&nbsp;</td>
            </tr>
</table> </td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
 
  <tr>
   <td bgcolor="#300942" class="TopBar" valign="top">
    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
     
      <tr>
       <td class="TopBar_0" style="max-width: 80px;width: 80px;" width="80">
        &nbsp;</td><td align="left" class="TopBar_logoAlly" valign="middle">
        <a alias="Ally Logo" href="https://www.ally.com/?CP=EML400003578" target="_blank"><img alt="Ally Do It Right logo" border="0" height="38" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/ea38ca95-e828-4af8-b7e8-5637d1800bef.png" style="display: block; max-width: 179px;" width="179"> </a></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">            
<tr>
             <td height="25" style="height: 25px; line-height: 25px; max-height: 25px">&nbsp;</td>
            </tr>
</table> </td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #300942; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        

        <!--[if gte mso 9]>            <xml>                 <o:OfficeDocumentSettings>                     <o:AllowPNG/>                     <o:PixelsPerInch>96</o:PixelsPerInch>                 </o:OfficeDocumentSettings>           </xml>         <![endif]-->

       <!--[if gt mso 15]>            <style>                table {                     border-collapse: collapse;                }                tr {                     font-size:0px;                     line-height:0px;                }                td.mobile-btn {                     font-size:16px !important;                     line-height:18px !important;                }                td.mod2-title {                     padding: 0px 40px 0px 0px !important;                }                td.mod3-widthIcon {                     padding: 0px 10px 0px 5px !important;                }            </style>         <![endif]-->

       <style type="text/css">
           @media screen and (max-width: 480px) {
             
                /* Hides content from the mobile view */
             
                .hide {
                   display: none !important;
                }
                .img_full_width {
                   width: 100% !important;
                   height: auto !important;
                }

                /* display desktop-hidden content on mobile */
             
                div.show {
                   display: block !important;
                   margin: 0px;
                   padding: 0px;
                   overflow: visible !important;
                   max-height: inherit !important;
                   padding: 0px;
                }
           }
       </style>
    </head>
  </html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table bgcolor="#300942" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
 
  <tr>
   <td bgcolor="#300942" class="hide">
    <a alias="Hero: Learn More" href="https://www.ally.com/bank/interest-checking-account/?CP=EML400003578" target="_blank" title="Learn More"><img align="center" alt="Image of a woman and a child looking at each other smiling. Text reads: Give your Ally Bank Savings Account a friend. An Ally Bank Spending Account can take care of all your checking needs. Button: Learn More" border="0" data-assetid="33520" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/14cf26ab-c03e-4025-83bc-c1460889da4e.png" style="display: block; width: 736px; max-width: 736px; padding: 0px; text-align: center; height: auto;" valign="middle" width="736"></a></td></tr><tr>
   <td border="0">
    <!--[if !mso 9]><!--><div class="show" style="font-size:0%; max-height: 0; overflow: hidden; display: none">
     <a alias="Hero: Learn More" href="https://www.ally.com/bank/interest-checking-account/?CP=EML400003578" target="_blank" title="Learn More"><img alt="Image of a woman and a child looking at each other smiling. Text reads: Give your Ally Bank Savings Account a friend. An Ally Bank Spending Account can take care of all your checking needs. Button: Learn More" class="img_full_width" height="" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/bdc84a38-54b1-492e-af35-46e62be9a635.png" style="display: block;" valign="top" width="100%"></a></div><!--<![endif]--></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner">
<table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;">
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        

        <!--[if gte mso 9]>            <xml>                 <o:OfficeDocumentSettings>                   <o:AllowPNG/>                   <o:PixelsPerInch>96</o:PixelsPerInch>                 </o:OfficeDocumentSettings>             </xml>         <![endif]-->

          <style type="text/css">
              @media screen and (max-width:480px) {
                td.BodyModule_Intro-text {
                    padding: 60px 20px 70px 20px !important;
                   }
               }
          </style>
    </head>  
  </html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #FFFFFF; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
 
  <tr>
   <td align="left" class="BodyModule_Intro-text" style="font-family: Helvetica, Arial, sans-serif; font-size:16px; letter-spacing: .5px; line-height: 24px; color:#300942; padding: 64px 80px 64px 80px;">
    Christopher, it's great that you're saving your money with an Ally Bank Savings Account. Why not open an Ally Bank Spending Account, too? When you have both, each account works harder for you. Don't miss out on the benefits and ease of banking under one roof.</td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table>
    <table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #650360; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        
        
        <!--[if mso | IE]>             <xml>                 <o:OfficeDocumentSettings>                     <o:AllowPNG/>                     <o:PixelsPerInch>96</o:PixelsPerInch>                 </o:OfficeDocumentSettings>             </xml>         <![endif]-->
      
        <!--[if gt mso 15]>            
<style>                .BodyModule_CircleNumber_iconCheck {                  line-height:45px !important;                   padding: 20px 10px 0px 20px !important;                }                td.BodyModule_CircleNumber_spaceBoxOutlook {                   line-height: 10px !important;                   height: 10px !important;                   max-height: 10px !important;                }                td.BodyModule_CircleNumber_textNumber {                 padding: 10px 40px 30px 10px !important;                 line-height: 24px !important;                }                td.BodyModule_CircleNumber_BoxMobile {                 padding: 0px 80px 0px 80px !important;                }   
                         </style>         <![endif]-->
      
        <style type="text/css">
            @media screen and (max-width:480px) {
                .img_full_width {
                  width: 100% !important;
                  height: auto !important;
                }
                .container {
                  width: 100% !important;
                  max-width: 100% !important;
                }
                .displayBlock {
                  display: block !important;
                }
                .padding0 {
                  padding: 0px 0px 0px 0px !important;
                }
                .hide {
                  display: none !important;
                }
                div.show {
                  display: block !important;
                  margin: 0px;
                  padding: 0px;
                  overflow: visible !important;
                  max-height: inherit !important;
                  padding: 0px;
                }
                
                .BodyModule_CircleNumber_title {
                  padding: 0px 20px 20px 20px !important;
                }

                .BodyModule_CircleNumber_copy {
                  padding: 0px 20px 40px 20px !important;
                }
                .BodyModule_CircleNumber_copy2 {
                  padding: 40px 20px 40px 20px !important;
                }
                .BodyModule_CircleNumber_textNumber {
                  padding: 0px 20px 20px 20px !important;
                }
                .BodyModule_CircleNumber_iconCheck {
                  padding: 0px 0px 10px 0px !important;
                }
                .BodyModule_CircleNumber_spaceBoxOutlook {
                  line-height: 10px !important;
                  height: 10px !important;
                }
               .BodyModule_CircleNumber_BoxMobile {
                padding: 0px 20px 0px 20px !important;
               }
               .BodyModule_buttonBox {
                      padding:30px 0px 10px 0px !important;
               }
               .container_button {
                      width: 100% !important;
               }
              }

        </style>
    </head>
  </html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="64" style="height: 64px">&nbsp;</td>
            </tr>
</table> </td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><!-- Start module --><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
 
  <tr>
   <td class="BodyModule_CircleNumber_BoxMobile" style="padding: 0px 80px 0px 80px;" valign="middle" width="100%">
    <table border="0" cellpadding="0" cellspacing="0" class="container" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
     
      <tr>
       <td style="font-family: Helvetica, Arial, sans-serif;color:#FFF7F0; font-weight: bold; font-size:30px;line-height:33px; padding:0px 0px 40px 0px">
        Take the stress out of spending.</td></tr><!-- box 1 --><tr>
       <td align="left" style="width: 100%" valign="top">
        <table bgcolor="#FFF7F0" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-radius: 10px; background-color: #FFF7F0;" width="100%">
         
          <tr>
           <td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_iconCheck displayBlock" style="line-height:px; border-radius: 10px;" valign="top">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td class="displayBlock" style="padding:20px 0px 20px 20px" valign="top">
                <img align="left" alt="Icon of an open cardboard box with a star over it" border="0" data-assetid="33986" height="60" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/fb8c56f4-aaaf-43cf-b928-d126e0bb31ce.png" style="display: block; width: 60px; max-width: 60px; padding: 0px; text-align: center; height: 60px;" valign="top" width="60"></td></tr></table></td><td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_textNumber displayBlock" style="border-radius: 10px; padding:20px 20px 20px 20px; background-color: #FFF7F0;" valign="middle">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td style="font-family: Helvetica, Arial, sans-serif;color:#300942; font-weight: normal; font-size:16px;line-height:24px;">
                Early direct deposit gets your paycheck to you up to 2 days early.</td></tr></table></td></tr></table></td></tr><!-- space --><tr>
       <td border="0" class="BodyModule_CircleNumber_spaceBoxOutlook" height="7" style="max-height:10px; height:10px; line-height:10px">
        &nbsp;</td></tr><!-- box 2 --><tr>
       <td align="left" style="width: 100%" valign="top">
        <table bgcolor="#FFF7F0" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-radius: 10px; background-color: #FFF7F0;" width="100%">
         
          <tr>
           <td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_iconCheck displayBlock" style="line-height:px; border-radius: 10px;" valign="top">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td class="displayBlock" style="padding:20px 0px 20px 20px" valign="top">
                <img align="left" alt="Icon of a calendar" border="0" data-assetid="33985" height="60" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/e3414f17-57f9-4a5f-b6d7-b5e84a5a528d.png" style="display: block; width: 60px; max-width: 60px; padding: 0px; text-align: center; height: 60px;" valign="top" width="60"></td></tr></table></td><td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_textNumber displayBlock" style="border-radius: 10px; padding:20px 30px 20px 20px; background-color: #FFF7F0;" valign="middle">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td style="font-family: Helvetica, Arial, sans-serif;color:#300942; font-weight: normal; font-size:16px;line-height:24px;">
                Use Allpoint<sup style="font-size: 11px; vertical-align: 5px;">®</sup> or MoneyPass<sup style="font-size: 11px; vertical-align: 5px;">®</sup> no-fee ATMs. Plus, we reimburse other ATM fees nationwide up to $10 per statement cycle.</td></tr></table></td></tr></table></td></tr><!-- space --><tr>
       <td border="0" class="BodyModule_CircleNumber_spaceBoxOutlook" height="10" style="max-height:10px; height:10px; line-height:10px">
        &nbsp;</td></tr><!-- box 3 --><tr>
       <td align="left" style="width: 100%" valign="top">
        <table bgcolor="#FFF7F0" border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-radius: 10px; background-color: #FFF7F0;" width="100%">
         
          <tr>
           <td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_iconCheck displayBlock" style="line-height:px; border-radius: 10px;" valign="top">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td class="displayBlock" style="padding:20px 0px 20px 20px" valign="top">
                <img align="left" alt="Icon of a money symbol" border="0" data-assetid="33985" height="60" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/a9894216-2321-4eb0-bafb-b2fcddec2ac4.png" style="display: block; width: 60px; max-width: 60px; padding: 0px; text-align: center; height: 60px;" valign="top" width="60"></td></tr></table></td><td align="left" bgcolor="#FFF7F0" class="BodyModule_CircleNumber_textNumber displayBlock" style="border-radius: 10px; padding:20px 30px 20px 20px; background-color: #FFF7F0;" valign="middle">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
             
              <tr>
               <td style="font-family: Helvetica, Arial, sans-serif;color:#300942; font-weight: normal; font-size:16px;line-height:24px;">
                No minimum amount to open and no monthly maintenance fees.</td></tr></table></td></tr></table></td></tr><!-- Start button --></table><table align="left" border="0" cellpadding="0" cellspacing="0" class="container_button" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; mso-line-height-rule: exactly;">
     
      <tr>
       <td class="BodyModule_Square_buttonBox" style="padding:50px 0px 0px 0px">
        <table border="0" cellpadding="0" cellspacing="0" class="container_button" role="button" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
         
          <tr>
           <td align="center" bgcolor="#FEE6FF" class="BodyModule_Square_button" height="60" style="font-family: Arial, Helvetica, Geneva, sans-serif; font-size: 21px; color: #954293; padding:0px 30px 0px 31px; border-radius: 4px; height: 60px;" valign="middle">
            <a alias="Middle: Open Account" href="https://www.ally.com/bank/interest-checking-account/?CP=EML400003578" style="text-decoration: none" target="_blank" title="Open Account"><span style="text-decoration: none; color: #954293;"><strong>Open Account</strong></span></a></td></tr></table></td></tr></table><!-- End button --></td></tr></table><!-- End module --></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="64" style="height: 64px">&nbsp;</td>
            </tr>
</table> </td></tr></table></td></tr></table></td></tr></table></td></tr></table>
<html lang="en">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    

    <!--[if gt mso 15]>            <style>                td.BodyModule_SquareMarks_Graph_icon {                 font-size:16px !important;                 line-height:20px !important;                }                td.BodyModule_SquareMarks_Graph_icon img {                 width:16px !important;                 padding: 5px 3px 0px 4px !important;                }            </style>         <![endif]-->

    <style type="text/css">
        @media screen and (max-width:480px) {
            .BodyModule_SquareMarks_Graph_BoxTitle2 {
                padding: 30px 10px 0px 10px !important;
            }

            .BodyModule_SquareMarks_Graph_BoxCheck {
                padding: 0px 20px 0px 20px !important;
            }

            .BodyModule_SquareMarks_Graph_IconTitle {
                padding: 10px 0px 20px 0px !important;
            }

            .BodyModule_SquareMarks_Graph_copy {
                padding: 0px 10px 30px 10px !important;
            }

            .BodyModule_SquareMarks_Graph_text {
                padding: 0px 0px 0px 0px !important;
            }

            .BodyModule_SquareMarks_Graph_ImgM {
                padding: 70px 10px 0px 10px !important;
            }

            .img_full_width {
                width: 100% !important;
                height: auto !important;
            }

            .container {
                width: 100% !important;
                max-width: 100% !important;
            }

            .displayBlock {
                display: block !important;
            }

            .padding0 {
                padding: 0px 0px 0px 0px !important;
            }

            .hide {
                display: none !important;
            }

            div.show {
                display: block !important;
                margin: 0px;
                padding: 0px;
                overflow: visible !important;
                max-height: inherit !important;
                padding: 0px;
            }

        }
    </style>
</head>
</html><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #FFFFFF; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    

    <!--[if gt mso 15]>            <style>                td.BodyModule_SquareMarks_Graph_icon {                 font-size:16px !important;                 line-height:20px !important;                }                td.BodyModule_SquareMarks_Graph_icon img {                 width:16px !important;                 padding: 5px 3px 0px 4px !important;                }            </style>         <![endif]-->

    <style type="text/css">
        @media screen and (max-width:480px) {
            .BodyModule_SquareMarks_Graph_BoxTitle2 {
                padding: 30px 20px 0px 20px !important;
            }

            .BodyModule_SquareMarks_Graph_BoxCheck {
                padding: 0px 20px 0px 20px !important;
            }

            .BodyModule_SquareMarks_Graph_IconTitle {
                padding: 10px 0px 20px 0px !important;
            }

            .BodyModule_SquareMarks_Graph_copy {
                padding: 0px 20px 30px 20px !important;
            }

            .BodyModule_SquareMarks_Graph_text {
                padding: 0px 0px 0px 0px !important;
            }
          
            .BodyModule_SquareMarks_Graph_ImgM {
                padding: 70px 20px 0px 20px !important;
            }

            .img_full_width {
                width: 100% !important;
                height: auto !important;
            }

            .container {
                width: 100% !important;
                max-width: 100% !important;
            }

            .displayBlock {
                display: block !important;
            }

            .padding0 {
                padding: 0px 0px 0px 0px !important;
            }

            .hide {
                display: none !important;
            }

            div.show {
                display: block !important;
                margin: 0px;
                padding: 0px;
                overflow: visible !important;
                max-height: inherit !important;
                padding: 0px;
            }

        }
    </style>
</head>
</html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td class="hide" height="50" style="height: 50px">&nbsp;</td>
            </tr>
</table> </td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><!-- Module --><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
 <!-- Start left content - image responsive -->
  <tr>
   <td class="displayBlock container padding0" style="max-width: 50%; padding: 20px 20px 20px 0px;" valign="middle" width="50%">
    <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
     
      <tr>
       <!-- image Desktop --><td class="hide">
        <img align="left" alt="Phone image with text that reads: Savings Bucket." border="0" class="img_full_width hide" data-assetid="32846" height="449" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/7171a9db-f1d5-4c5f-b994-b766ff81dc11.png" style="display: block; width: 360; max-width: 360px; padding: 0px; text-align: center; height: 449px;" valign="middle" width="360"></td></tr><tr>
       <!-- image Mobile --><td border="0">
        <!--[if !mso 9]><!--><div class="show BodyModule_SquareMarks_Graph_ImgM" style="font-size:0%; max-height: 0; overflow: hidden; display: none">
         <img alt="Phone image with text that reads: Savings Bucket." class="img_full_width" height="" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/97111852-8bb0-4e42-aca1-c429743476d5.png" style="display: block;" valign="top" width="100%"></div><!--<![endif]--></td></tr></table></td><!-- End Start left content - image responsive --><!-- Start right content --><td class="displayBlock container padding0" style="max-width: 50%; padding: 0px 60px 0px 10px;" valign="middle" width="50%">
    <table border="0" cellpadding="0" cellspacing="0" class="container" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
     <!-- icon - title -->
      <tr>
       <td class="BodyModule_SquareMarks_Graph_BoxTitle2 img_full_width" style="padding:0px 0px 10px 0px">
        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
         
          <tr>
           <td align="left" class="BodyModule_SquareMarks_Graph_IconTitle displayBlock" style="padding:0px 0px 0px 0px; font-family: Helvetica, Arial, sans-serif;color:#300942; font-weight: bold; font-size:30px;line-height:33px;" valign="middle">
            Connected<br>
            accounts help you<br>
            budget and save<br>
            better.</td></tr></table></td></tr><tr>
       <td class="BodyModule_SquareMarks_Graph_copy" style="font-family: Helvetica, Arial, sans-serif;color:#300942; font-size:16px;line-height:24px; padding: 0px 20px 20px 0px">
        <span style="color:#300942; text-decoration: none; font-weight: bold;">Link your accounts to save with round ups.</span><br>
        As you spend, we round up your total to the nearest dollar. When round ups add up to $5, it goes to your Savings Account.</td></tr><tr>
       <td class="BodyModule_SquareMarks_Graph_copy" style="font-family: Helvetica, Arial, sans-serif;color:#300942; font-size:16px;line-height:24px; padding: 0px 20px 0px 0px">
        <span style="color:#300942; text-decoration: none; font-weight: bold;">Use buckets to sort your spending and saving money.</span><br>
        <a alias="Organized money" href="https://www.ally.com/stories/save/how-to-use-spending-and-savings-buckets-together/?CP=EML400003578" style="color: #0071C4; text-decoration: none; font-weight:bold;" target="_blank" title="Organized money"><span style="color:#0071C4; text-decoration: none; font-weight: bold;">Organized money</span> </a> is easier to manage so you can pay your bills and pursue your goals with confidence.</td></tr></table></td></tr></table><!-- End module --></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr>
<td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="50" style="height: 50px">&nbsp;</td>
            </tr>
</table> </td></tr></table></td></tr></table></td></tr></table></td></tr></table>



<table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #FFF7F0; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
         
        <style type="text/css">
              @media screen and (max-width:480px) {
                  .BannerModule_Subhead_Img {
                      padding: 30px 0px 15px 0px !important;
                  }
                  .BannerModule_Subhead_Copy {
                      padding: 20px 20px 0px 20px !important;
                  }
                  .BannerModule_CTA {
                      padding: 20px 20px 0px 20px !important;
                  }
                 .padding0 {
                      padding: 0px !important;
                  }
                 .BodyModule_buttonBox {
                      padding:30px 0px 10px 0px !important;
                 }
                .container_button {
                      width: 100% !important;
                 }
                .lower_banner_box {
                     padding:30px 0px 0px 20px !important;
                 }
             }
      </style>
    </head>
  </html><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" valign="middle" align="left" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
    <tr>
        <td valign="middle" class="lower_banner_box hide" style="padding: 60px 0px 0px 80px; font-family: Helvetica, Arial, sans-serif;font-size:16px;color:#300942; font-weight: normal;">
            <img src="https://image.email.ally.com/lib/fec2157777660d75/m/1/214e8b87-23c8-4d65-b2f9-599f4b2fd01c.png" width="538" align="left" valign="middle" alt="Enjoy the benefits of banking in one place." style="display: block; width: 538px; max-width: 538px;" border="0" class="img_full_width">
        </td>
    </tr>

    <tr>
        <td valign="middle" class="lower_banner_box" style="padding: 30px 0px 0px 80px; font-family: Helvetica, Arial, sans-serif;font-size:16px;color:#300942; font-weight: normal;">
            <!--[if !mso 9]><!-->
            <div class="show" style="font-size:0%; max-height: 0; overflow: hidden; display: none" align="left">
                <img align="left" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/c659bb64-1401-4978-8b1b-58f59f8afbbe.png" width="260" height="" valign="top" alt="Enjoy the benefits of banking in one place." style="display: block; width: 260px; max-width: 260px !important; height: auto !important" class="BannerModule_Subhead_Img">

            </div>
            <!--<![endif]-->
        </td>
    </tr>
</table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table align="left" border="0" cellpadding="0" cellspacing="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
 
  <tr>
   <td class="BannerModule_Subhead_Copy img_full_width" style="padding:0px 50px 0px 80px">
    <table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
     
      <tr>
       <td align="left" class="padding0" style="line-height:18px; color: #300942; font-size: 24px; line-height:30px; font-family:Helvetica, sans-serif; padding:0px 0px 0px 0px;" valign="top" width="100%">
        Open an Ally Bank Spending Account and watch the money magic happen between your accounts.</td></tr></table></td></tr><tr>
   <td align="left" class="BannerModule_CTA img_full_width" style="padding:40px 0px 0px 80px; width: 100% !important;">
    <table align="left" border="0" cellpadding="0" cellspacing="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
     <!-- button -->
      <tr>
       <td>
        <table align="left" border="0" cellpadding="0" cellspacing="0" class="container_button">
         <!-- button -->
          <tr>
           <td class="BodyModule_buttonBox" style="padding:0px 0px 0px 0px">
            <table border="0" cellpadding="0" cellspacing="0" class="container_button" role="button" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
             
              <tr>
               <td align="center" bgcolor="#954293" class="BodyModule_button" height="60" style="font-family: Arial, Helvetica, Geneva, sans-serif; font-size: 21px; color: #FFF7F0; padding:0px 30px 0px 31px; border-radius: 4px; height: 60px;" valign="middle">
                <a alias="Lower Banner CTA: Open Account" href="https://www.ally.com/bank/interest-checking-account/?CP=EML400003578" style="text-decoration: none" target="_blank" title="Open Account"><span style="text-decoration: none; color: #FFF7F0;"><strong>Open Account</strong></span></a></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="64" style="height: 64px">&nbsp;</td>
            </tr>
</table> </td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: #300942; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        

        <!--[if gte mso 9]>              <xml>                <o:OfficeDocumentSettings>                  <o:AllowPNG/>                  <o:PixelsPerInch>96</o:PixelsPerInch>                </o:OfficeDocumentSettings>              </xml>         <![endif]-->

        <!--[if gt mso 15]>             <style>                .footerLitany_lineOutlook {                      line-height:1px !important;                      height:1px !important;                 }                 .footerLitany_width20 {                       width: 80px !important;                       height: auto !important;                 }                 .footerLitany_padding2 {                       line-height:1px !important                       height: 1px !important;                       padding: 0px 0px 10px 0px !important;                 }             </style>         <![endif]-->

        <style type="text/css">
              @media screen and (max-width:480px) {
                    body,
                    table,
                    td,
                    div,
                    span,
                    li {
                        font-family: Helvetica, Arial, sans-serif !important;
                    }
                    .img_full_width {
                        width: 100% !important;
                        height: auto !important;
                    }

                    /* LITANY */

                    .footerLitany_img_80_width {
                        width: 80% !important;
                        height: auto !important;
                    }
                    img.footerLitany_img_90_widthLine {
                        width: 90% !important;
                        height: 1px !important;
                        padding: 0px 0px 0px 0px !important
                    }
                    .displayBlock {
                        display: block !important;
                    }
                    .footerLitany_padding0 {
                        padding: 0px !important;
                    }
                    .footerLitany_padding1 {
                        padding: 10px 0px 10px 0px !important;
                    }
                    .footerLitany_padding2 {
                        padding: 0px 0px 20px 0px !important;
                    }
                    .footerLitany_padding3 {
                        padding: 0px 0px 0px 0px !important;
                    }
                    .footerLitany_padding4 {
                        padding: 20px 0px 0px 0px !important;
                    }
                    .footerLitany_dropSocialcell {
                        display: block !important;
                        width: 100% !important;
                        text-align: left !important;
                        padding: 0px 0px 0px 0px !important;
                    }
                    .footerLitany_width20 {
                        width: 20px !important;
                    }
              }
         </style>
    </head>
  </html></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table align="center" border="0" cellpadding="0" cellspacing="0" class="img_full_width" role="presentation" style="padding:0px 0px 0px 0px;mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
 
  <tr>
   <td align="left" class="img_full_width" style="vertical-align:top !important; border:0;" valign="top">
    <table border="0" cellpadding="0" cellspacing="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" valign="top" width="100%">
     <!-- Image -->
      <tr>
       <td class="footerLitany_width20" style="width: 80px; max-width:80px" width="80">
        &nbsp;</td><td style="padding: 30px 0px 40px 0px; vertical-align: top !important; color: #fff;" valign="top">
        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
         
          <tr>
           <td class="footerLitany_padding1" style="font-family: Helvetica, Arial, sans-serif; font-size:14px; color:#FFF7F0; padding:0px 0px 12px 0px; ">
            We're all better off with an ally.</td></tr><tr>
           <td class="footerLitany_padding2" style="padding:0px 0px 20px 0px;">
            <img alt="We're all better off with an ally - digitally, financially, personally" class="footerLitany_img_80_width" height="26" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/49efc7fa-788e-447f-8f55-c2c38e504bc7.png" style="width:270px; max-width:100%; height: 26px; border: 0px !important; display: block;" width="270"></td></tr><!-- line with bgcolor--><tr>
           <td align="center" bgcolor="#ffffff" class="footerLitany_lineOutlook" height="1" style="border-top: 1px solid #ffffff; line-height: 1px !important; mso-line-height-rule: exactly; height: 1px; max-height: 1px; width: 100%; font-size: 1px;" valign="top" width="576">
           </td></tr><!-- end line with bgcolor --><!-- text + AppButton --><tr>
           <td class="footerLitany_padding4" style="font-family:  Helvetica, Arial, sans-serif; font-size:15px; color:#2A2A2A; padding:15px 0px 0px 0px; line-height:24px;">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
             
              <tr>
               <td align="left" class="footerLitany_dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#ffffff; padding:0px 0px 0px 0px; line-height: 16px;" valign="top">
                <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
                 
                  <tr>
                   <td align="left" class="footerLitany_dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#FFF7F0; padding:0px 0px 0px 0px; line-height: 16px; font-weight: normal;" valign="top">
                    The Ally Mobile app lets you manage your<br>
                    accounts&nbsp;on the go. It's fast, secure and free.</td></tr></table></td><td class="footerLitany_dropSocialcell" height="25" width="1">
                &nbsp;</td><td align="left" class="footerLitany_dropSocialcell displayBlock" valign="top" width="185">
                <table align="left" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
                 
                  <tr>
                   <td align="right" style="padding:2px 10px 0px 0px;" valign="top">
                    <a alias="Button to Ally Mobile app on the Apple App Store" href="https://apps.apple.com/us/app/ally-banking-investing/id514374715" style="text-decoration: none; color: #FFF7F0;" target="_blank" title="Button to Ally Mobile app on the Apple App Store"><img alt="Button to Ally Mobile app on the Apple App Store" height="32" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/55a97086-b32c-478c-98b4-6491dff0f807.png" style="display:block; border:0;" width="108"></a></td><td align="right" style="padding:2px 0px 0px 0px;" valign="top">
                    <a alias="Button to Ally Mobile app on the Google Play Store" href="https://play.google.com/store/apps/details?id=com.ally.MobileBanking&hl=en_US&gl=US" style="text-decoration:none; color: #FFF7F0;" target="_blank" title="Button to Ally Mobile app on the Google Play Store"><img alt="Button to Ally Mobile app on the Google Play Store" height="32" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/83719ac4-15cf-40a4-be8d-1ed9059900f4.png" style="display:block; border:0;" width="108"></a></td></tr></table></td></tr></table></td></tr></table></td><td class="footerLitany_width20" style="width: 80px; max-width:80px" width="80">
        &nbsp;</td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="10" style="height: 10px; line-height: 10px; font-size:10px">&nbsp;</td>
            </tr>
</table> </td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        
      
        <!--[if mso | IE]>             <xml>                 <o:OfficeDocumentSettings>                     <o:AllowPNG/>                     <o:PixelsPerInch>96</o:PixelsPerInch>                 </o:OfficeDocumentSettings>             </xml>         <![endif]-->
      
        <style type="text/css">
            @media screen and (max-width:480px) {
                  .dropSocialcell {
                      display: block !important; 
                      max-width: 100% !important;
                      text-align: left !important;
                      padding: 0px 0px 0px 20px !important;
                   }
                  .dropSocialcell img {
                      width: 15px !important;
                      height: auto !important;
                   }
                   .img_full_width {
                      width: 100% !important;
                      height: auto !important;
                   }
                   .footer_box {
                      padding: 0px 20px 0px 20px !important;
                   }
            }
        </style>
    </head>
  </html><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 30px 0px 0px; " class="stylingblock-content-wrapper camarker-inner"><table align="left" border="0" cellpadding="0" cellspacing="0" class="container" role="presentation" width="100%">
  <tr>
   <td align="left" class="img_full_width" style="vertical-align:top !important; border:0;" valign="top">
    <table border="0" cellpadding="0" cellspacing="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" valign="top" width="100%">
     <!-- Image -->
      <tr>
       <td class="footerLitany_width20" style="width: 80px; max-width:80px" width="80">
        &nbsp;</td><td style="padding: 30px 0px 40px 0px; vertical-align: top !important; color: #fff;" valign="top">
        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
         
          <tr>
           <td class="footerLitany_padding1" style="font-family: Helvetica, Arial, sans-serif; font-size:14px; color:#FFF7F0; padding:0px 0px 12px 0px; ">
            We're all better off with an ally.</td></tr><tr>
           <td class="footerLitany_padding2" style="padding:0px 0px 20px 0px;">
            <img alt="We're all better off with an ally - digitally, financially, personally" class="footerLitany_img_80_width" height="26" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/49efc7fa-788e-447f-8f55-c2c38e504bc7.png" style="width:270px; max-width:100%; height: 26px; border: 0px !important; display: block;" width="270"></td></tr><!-- line with bgcolor--><tr>
           <td align="center" bgcolor="#ffffff" class="footerLitany_lineOutlook" height="1" style="border-top: 1px solid #ffffff; line-height: 1px !important; mso-line-height-rule: exactly; height: 1px; max-height: 1px; width: 100%; font-size: 1px;" valign="top" width="576">
           </td></tr><!-- end line with bgcolor --><!-- text + AppButton --><tr>
           <td class="footerLitany_padding4" style="font-family:  Helvetica, Arial, sans-serif; font-size:15px; color:#2A2A2A; padding:15px 0px 0px 0px; line-height:24px;">
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly" width="100%">
             
              <tr>
               <td align="left" class="footerLitany_dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#ffffff; padding:0px 0px 0px 0px; line-height: 16px;" valign="top">
                <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
                 
                  <tr>
                   <td align="left" class="footerLitany_dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#FFF7F0; padding:0px 0px 0px 0px; line-height: 16px; font-weight: normal;" valign="top">
                    The Ally Mobile app lets you manage your<br>
                    accounts&nbsp;on the go. It's fast, secure and free.</td></tr></table></td><td class="footerLitany_dropSocialcell" height="25" width="1">
                &nbsp;</td><td align="left" class="footerLitany_dropSocialcell displayBlock" valign="top" width="185">
                <table align="left" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;mso-line-height-rule: exactly">
                 
                  <tr>
                   <td align="right" style="padding:2px 10px 0px 0px;" valign="top">
                    <a alias="Button to Ally Mobile app on the Apple App Store" href="https://apps.apple.com/us/app/ally-banking-investing/id514374715" style="text-decoration: none; color: #FFF7F0;" target="_blank" title="Button to Ally Mobile app on the Apple App Store"><img alt="Button to Ally Mobile app on the Apple App Store" height="32" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/55a97086-b32c-478c-98b4-6491dff0f807.png" style="display:block; border:0;" width="108"></a></td><td align="right" style="padding:2px 0px 0px 0px;" valign="top">
                    <a alias="Button to Ally Mobile app on the Google Play Store" href="https://play.google.com/store/apps/details?id=com.ally.MobileBanking&hl=en_US&gl=US" style="text-decoration:none; color: #FFF7F0;" target="_blank" title="Button to Ally Mobile app on the Google Play Store"><img alt="Button to Ally Mobile app on the Google Play Store" height="32" src="https://image.email.ally.com/lib/fe5915707c61037e711d/m/5/83719ac4-15cf-40a4-be8d-1ed9059900f4.png" style="display:block; border:0;" width="108"></a></td></tr></table></td></tr></table></td></tr></table></td><td class="footerLitany_width20" style="width: 80px; max-width:80px" width="80">
        &nbsp;</td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="min-width: 100%; " class="stylingblock-content-wrapper"><tr><td class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;">            
<tr>
             <td height="10" style="height: 10px; line-height: 10px; font-size:10px">&nbsp;</td>
            </tr>
</table> </td></tr></table><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px; " class="stylingblock-content-wrapper camarker-inner"><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-bottom: 0px;"><!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
  <html lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
      
        <!--[if mso | IE]>
            <xml>
                <o:OfficeDocumentSettings>
                    <o:AllowPNG/>
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                </o:OfficeDocumentSettings>
            </xml>
        <![endif]-->
      
        <style type="text/css">
            @media screen and (max-width:480px) {
                  .dropSocialcell {
                      display: block !important; 
                      max-width: 100% !important;
                      text-align: left !important;
                      padding: 0px 0px 0px 20px !important;
                   }
                  .dropSocialcell img {
                      width: 15px !important;
                      height: auto !important;
                   }
                   .img_full_width {
                      width: 100% !important;
                      height: auto !important;
                   }
                   .footer_box {
                      padding: 0px 20px 0px 20px !important;
                   }
            }
        </style>
    </head>
  </html><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 30px 0px 0px; " class="stylingblock-content-wrapper camarker-inner"><table align="left" border="0" cellpadding="0" cellspacing="0" class="container" role="presentation" width="100%">
 
  <tr>
   <td align="left" style="font-family: Arial,Helvetica, sans-serif; font-size:12px; color:#650360; padding:0px 80px 25px 0px;" valign="top" width="100%">
    <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
     
      <tr>
       <th align="left" class="dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#ffffff; padding:0px 35px 0px 0px; line-height: 16px;" valign="middle">
       </th><th class="dropSocialcell" height="15" style="font-size: 15px; line-height: 15px;" width="1">
       </th><th align="right" class="dropSocialcell" style="font-family: Helvetica, Arial, sans-serif; font-size:12px; color:#ffffff; padding:0px 0px 0px 0px;line-height: 16px;" valign="top" width="185">
        <table border="0" cellpadding="0" cellspacing="0" role="presentation">
         
          <tr>
           <td align="left" style="padding:5px 32px 0px 0px;">
            <a alias="Ally Instagram button" conversion="false" href="https://www.instagram.com/ally/?hl=en" target="_blank" title="Ally Instagram button"><img alt="Ally Instagram button" data-assetid="31633" height="16" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/96121ec3-2e95-49c8-ba79-1fd89a593d39.png" style="display: block; border: 0px; padding: 0px; text-align: center; height: 16px; width: 16px;" width="16"></a></td><td align="left" style="padding:5px 32px 0px 0px;">
            <a alias="Ally Facebook button" conversion="false" href="https://www.facebook.com/ally" target="_blank" title="Ally Facebook button"><img alt="Ally Facebook button" data-assetid="31634" height="16" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/66978010-20ac-4104-9b97-8d72b827dd18.png" style="display: block; border: 0px; padding: 0px; text-align: center; height: 16px; width: 16px;" width="16"></a></td><td align="left" style="padding:5px 0px 0px 0px;">
            <a alias="Ally Twitter button" conversion="false" href="https://twitter.com/Ally" target="_blank" title="Ally Twitter button"><img alt="Ally Twitter button" data-assetid="31635" height="13" src="https://image.email.ally.com/lib/fec2157777660d75/m/1/7783ac7e-12e4-4b1f-a3eb-c8e5d06632ee.png" style="display: block; padding: 0px; height: 13px; width: 16px; text-align: center; border: 0px;" width="16"></a></td></tr></table></th></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr><tr><td><table cellspacing="0" cellpadding="0" role="presentation" style="width: 100%;"><tr><td valign="top" class="responsive-td" style="width: 100%; padding-top: 0px;"><table cellpadding="0" cellspacing="0" width="100%" role="presentation" style="background-color: transparent; min-width: 100%; " class="stylingblock-content-wrapper"><tr><td style="padding: 0px 20px 0px 0px; " class="stylingblock-content-wrapper camarker-inner"><table border="0" cellpadding="0" cellspacing="0" class="img_full_width" role="presentation">
 
  <tr>
   <td align="left" class="footer_box" style="line-height:18px; color: #650360; font-size: 12px; font-family:Helvetica, sans-serif; padding:20px 40px 0px 80px;" valign="middle">
    <strong>Questions?</strong><br>
    <strong><strong><a alias="Chat with us anytime" conversion="false" href="https://www.ally.com/contact-us/#ally-bank" style="color:#0071C4;text-decoration:none; font-weight: bold;" target="_blank" title="Chat with us anytime"><span style="color:#0071C4; text-decoration: none; font-weight: bold;"><strong>Chat with us anytime</strong></span></a></strong></strong> or call us 24/7 at <strong><strong><a alias="**************" conversion="false" href="tel:**************" style="color:#0071C4;text-decoration:none; font-weight: bold;" target="_blank" title="**************"><span style="color:#0071C4; text-decoration: none; font-weight: bold;"><strong>**************</strong></span></a></strong></strong><br>
    <br>
    <span class="applelinkswhite">&copy;2009 - %%xtyear%% Ally Financial Inc.</span><br>
    Deposits products are offered by Ally Bank, <a alias="Footer: Member FDIC" href="https://www.ally.com/bank/fdic/" style="text-decoration: none; color: #0071C4;" target="_blank" title="Member FDIC"><span style="color: #0071C4; text-decoration: none;"><strong>Member FDIC</strong></span></a><br>
    Ally and Ally Bank are registered service marks of Ally Financial Inc.<br>
    Ally Bank Customer Care Department, P.O. Box&nbsp;951, Horsham,&nbsp;PA&nbsp;19044<br>
    <a href="https://www.ally.com/privacy/" style="color: #0071C4; text-decoration: none; font-weight:bold;" target="_blank" title="Privacy"><span style="color: #0071C4; text-decoration: none; font-weight:bold;"><strong>Privacy</strong></span></a>&nbsp;&nbsp;|&nbsp;&nbsp;<a href="https://www.ally.com/security/" style="color: #0071C4; text-decoration: none; font-weight:bold;" target="_blank" title="Security"><span style="color: #0071C4; text-decoration: none; font-weight:bold;"><strong>Security</strong></span></a><p>
    Savings buckets and boosters are features of Ally Bank %%=v(@OSA_VAR)=%%.</p>
<p>
     Spending buckets are a feature of the Ally Bank %%=v(@ICA_VAR)=%%.</p><p>
     Boosters are features of the Ally Bank %%=v(@OSA_VAR)=%%.</p><p>
     Apple and the Apple logo are trademarks of Apple Inc., registered in the U.S. and other countries and regions. App Store is a service mark of Apple Inc.&nbsp;</p><p>
     Google Play and the Google Play logo are trademarks of Google LLC.</p>You can <a alias="Unsubscribe" href="%%unsub_center_url%%" style="text-decoration:none; color:#0071C4;" target="_blank" title="Unsubscribe"><span style="color:#0071C4; text-decoration:none; font-weight: normal;"><strong>unsubscribe</strong></span></a> from Ally Bank marketing emails at any time. Please do not reply to this email.</td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table>
                                                </td>
                                              </tr>
                                              </tbody>
                                            </table>
                                          </td>
                                        </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      
    </table>
 <custom name="opencounter" type="tracking">
  </body>
</html>
