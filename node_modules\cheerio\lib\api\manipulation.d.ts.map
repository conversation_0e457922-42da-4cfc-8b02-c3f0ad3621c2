{"version": 3, "file": "manipulation.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/manipulation.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAc,OAAO,EAA8B,MAAM,YAAY,CAAC;AAK7E,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,KAAK,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAErE;;;;;;;;GAQG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,OAAO,EAC7C,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,IAAI,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,EAClC,KAAK,CAAC,EAAE,OAAO,GACd,OAAO,EAAE,CAiBX;AA8GD;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,OAAO,EACxC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAClC,OAAO,CAAC,CAAC,CAAC,CAMZ;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,OAAO,EACzC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAClC,OAAO,CAAC,CAAC,CAAC,CAMZ;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,MAAM,0DAnLD,OAAO,KACV,MAAM,QACH,MAAM,KACT,mBAAmB,OAAO,CAAC,gDAkLxC,CAAC;AAEH;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,OAAO,0DA1MF,OAAO,KACV,MAAM,QACH,MAAM,KACT,mBAAmB,OAAO,CAAC,gDAyMxC,CAAC;AAuDH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AACH,eAAO,MAAM,IAAI,iDAtFJ,cAAc,OAAO,CAAC,eAqGjC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,eAAO,MAAM,SAAS,iDAlJT,cAAc,OAAO,CAAC,eAsJjC,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,wBAAgB,MAAM,CAAC,CAAC,SAAS,OAAO,EACtC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,CAAC,CAAC,CAOZ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,OAAO,EACvC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,GACxB,OAAO,CAAC,CAAC,CAAC,CAiCZ;AAID;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAgB,KAAK,CAAC,CAAC,SAAS,OAAO,EACrC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,GAAG,KAAK,EACJ,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAC,GACzE,kBAAkB,CAAC,OAAO,CAAC,EAAE,GAChC,OAAO,CAAC,CAAC,CAAC,CA0BZ;AAID;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAC3C,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAClC,OAAO,CAAC,CAAC,CAAC,CA6BZ;AAID;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAgB,MAAM,CAAC,CAAC,SAAS,OAAO,EACtC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,GAAG,KAAK,EACJ,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,kBAAkB,CAAC,OAAO,CAAC,CAAC,GACzE,kBAAkB,CAAC,OAAO,CAAC,EAAE,GAChC,OAAO,CAAC,CAAC,CAAC,CA0BZ;AAID;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,YAAY,CAAC,CAAC,SAAS,OAAO,EAC5C,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,MAAM,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAClC,OAAO,CAAC,CAAC,CAAC,CA2BZ;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,MAAM,CAAC,CAAC,SAAS,OAAO,EACtC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,CAAC,CAAC,CAUZ;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAC3C,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,GAC9B,OAAO,CAAC,CAAC,CAAC,CA2BZ;AAED;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CASrE;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC;AACzE;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GACvB,OAAO,CAAC,CAAC,CAAC,CAAC;AAyBd;;;;;GAKG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAEpE;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AAClE;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,MAAM,CAAC,GACjE,OAAO,CAAC,CAAC,CAAC,CAAC;AA6Bd;;;;;;;;;;;;GAYG;AACH,wBAAgB,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAErE"}