{"version": 3, "file": "sort.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["sort.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,YAAY,EAAkB,MAAM,UAAU,CAAC;AAEzE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAmC;IACxD,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,CAAC;IAC5B,CAAC,YAAY,CAAC,GAAG,EAAE,EAAE,CAAC;IACtB,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;IAC3B,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC;CAC3B,CAAC,CAAC;AAEH,MAAM,UAAU,WAAW,CAAC,KAAuB;IAC/C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,UAAU,GAAG,IAAI,GAAG,CAA0B;IAChD,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC;IAC5B,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3B,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;IACxB,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1B,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;IACxB,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;CAC3B,CAAC,CAAC;AAEH;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,UAAU,eAAe,CAAC,GAAuB;IAC3D,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzB,IAAI,OAAO,GAAG,CAAC;YAAE,SAAS;QAE1B,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACnD,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACzB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACpB,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACf,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;SACtB;KACJ;AACL,CAAC;AAED,SAAS,YAAY,CAAC,KAAuB;;IACzC,IAAI,IAAI,GAAG,MAAA,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,mCAAI,CAAC,CAAC,CAAC;IAE3C,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE;QACvC,IAAI,GAAG,MAAA,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,mCAAI,CAAC,CAAC;QAEzC,IAAI,KAAK,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;YAChE,gCAAgC;YAChC,IAAI,GAAG,CAAC,CAAC;SACZ;QAED,IAAI,KAAK,CAAC,UAAU,EAAE;YAClB;;;eAGG;YACH,IAAI,KAAK,CAAC,CAAC;SACd;KACJ;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM,EAAE;QAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACb,IAAI,GAAG,CAAC,CAAC;SACZ;aAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;YAC1D,IAAI,GAAG,CAAC,CAAC,CAAC,wBAAwB;SACrC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAClC,qBAAqB;YACrB,IAAI,GAAG,IAAI,CAAC,GAAG,CACX,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAC7D,CAAC;YAEF,8DAA8D;YAC9D,IAAI,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,CAAC;aACZ;SACJ;aAAM;YACH,IAAI,GAAG,CAAC,CAAC;SACZ;KACJ;IACD,OAAO,IAAI,CAAC;AAChB,CAAC"}