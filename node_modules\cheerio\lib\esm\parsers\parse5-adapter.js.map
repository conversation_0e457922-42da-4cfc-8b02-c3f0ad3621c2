{"version": 3, "file": "parse5-adapter.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["parsers/parse5-adapter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAqB,UAAU,EAAc,MAAM,YAAY,CAAC;AACvE,OAAO,EAAE,KAAK,IAAI,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,QAAQ,CAAC;AAC/E,OAAO,EAAE,OAAO,IAAI,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AAGhF;;;;;;;;GAQG;AACH,MAAM,UAAU,eAAe,CAC7B,OAAe,EACf,OAAwB,EACxB,UAAmB,EACnB,OAA0B;IAE1B,MAAM,IAAI,GAAG;QACX,gBAAgB,EACd,OAAO,OAAO,CAAC,gBAAgB,KAAK,SAAS;YAC3C,CAAC,CAAC,OAAO,CAAC,gBAAgB;YAC1B,CAAC,CAAC,IAAI;QACV,WAAW,EAAE,kBAAkB;QAC/B,sBAAsB,EAAE,OAAO,CAAC,sBAAsB;KACvD,CAAC;IAEF,OAAO,UAAU;QACf,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;QAC9B,CAAC,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,GAAG,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;AAEvD;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAiC;IAChE;;;;OAIG;IACH,MAAM,KAAK,GAAG,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;YACpB,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChE;KACF;IAED,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE;QACpD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1B,MAAM,IAAI,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC5C;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}