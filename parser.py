from bs4 import BeautifulSoup, Tag
import re

# (Keep parse_inline_styles and map_styles_to_mjml_attrs as they are)
def parse_inline_styles(style_string):
    """
    Parses an inline style string and returns a dictionary of CSS properties.
    """
    styles = {}
    if style_string:
        for declaration in style_string.split(';'):
            if ':' in declaration:
                prop, value = declaration.split(':', 1)
                styles[prop.strip()] = value.strip()
    return styles

def map_styles_to_mjml_attrs(styles):
    """
    Maps CSS properties from a style dictionary to MJML attributes.
    Returns two dictionaries: one for direct MJML attributes and one for content styles.
    """
    mjml_attrs = {}
    content_styles = {} # Styles that might apply to the inner mj-text content

    style_map = {
        'background-color': 'background-color',
        'color': 'color',
        'font-family': 'font-family',
        'font-size': 'font-size',
        'font-weight': 'font-weight',
        'line-height': 'line-height',
        'text-align': 'align', # MJML uses 'align' for text alignment
        'padding': 'padding',
        'padding-top': 'padding-top',
        'padding-bottom': 'padding-bottom',
        'padding-left': 'padding-left',
        'padding-right': 'padding-right',
        'border-radius': 'border-radius',
        'width': 'width',
        'height': 'height',
        'vertical-align': 'vertical-align', # For td, maps to mj-column
        'text-transform': 'text-transform',
        'text-decoration': 'text-decoration',
        'letter-spacing': 'letter-spacing',
    }

    # Iterate through common style properties and map them
    for css_prop, mjml_attr in style_map.items():
        if css_prop in styles:
            mjml_attrs[mjml_attr] = styles[css_prop]

    # Handle margin specifically for mj-text, mj-image etc.
    if 'margin' in styles:
        content_styles['margin'] = styles['margin']
    if 'margin-top' in styles:
        content_styles['margin-top'] = styles['margin-top']
    if 'margin-bottom' in styles:
        content_styles['margin-bottom'] = styles['margin-bottom']
    if 'margin-left' in styles:
        content_styles['margin-left'] = styles['margin-left']
    if 'margin-right' in styles:
        content_styles['margin-right'] = styles['margin-right']


    # Special handling for background on elements that can take it
    if 'background' in styles:
        mjml_attrs['background-url'] = re.search(r'url\((.*?)\)', styles['background'])
        if mjml_attrs['background-url']:
            mjml_attrs['background-url'] = mjml_attrs['background-url'].group(1).strip("'\"")
            del styles['background'] # Remove if handled by background-url
        if 'background-color' not in mjml_attrs:
            # Try to extract color if not already set
            color_match = re.search(r'#[0-9a-fA-F]{3,6}|\b(?:rgb|rgba|hsl|hsla)\([^)]+\)|\b[a-zA-Z]+\b', styles['background'])
            if color_match:
                mjml_attrs['background-color'] = color_match.group(0)

    return mjml_attrs, content_styles


def convert_html_node_to_mjml(node):
    """
    Recursively converts a BeautifulSoup node (tag or NavigableString) to MJML JSON.
    Returns a single MJML component dict, or a list of components, or None.
    """
    if isinstance(node, Tag):
        mjml_element = {"tagName": "", "attributes": {}, "children": []}

        # Extract inline styles and map them
        inline_styles = parse_inline_styles(node.get('style'))
        mjml_attrs, content_styles_for_mjtext = map_styles_to_mjml_attrs(inline_styles)
        mjml_element['attributes'].update(mjml_attrs)

        # Transfer common HTML attributes
        for attr in ['id', 'class', 'width', 'height', 'align', 'valign', 'href', 'src', 'alt', 'title']:
            if node.has_attr(attr):
                if attr == 'width' and node.name in ['img', 'table']:
                    mjml_element['attributes']['width'] = node[attr]
                elif attr == 'height' and node.name == 'img':
                    mjml_element['attributes']['height'] = node[attr]
                elif attr == 'align' and node.name in ['table', 'div', 'p']:
                    mjml_element['attributes']['align'] = node[attr]
                elif attr == 'valign' and node.name == 'td':
                     mjml_element['attributes']['vertical-align'] = node[attr]
                elif attr == 'href' and node.name == 'a':
                    mjml_element['attributes']['href'] = node[attr]
                elif attr == 'src' and node.name == 'img':
                    mjml_element['attributes']['src'] = node[attr]
                elif attr == 'alt' and node.name == 'img':
                    mjml_element['attributes']['alt'] = node['alt']
                elif attr in ['id', 'class', 'title']:
                    mjml_element['attributes'][attr] = node[attr]


        # --- Specific Tag Mapping ---

        # HTML and BODY tags are special. They primarily serve to hold other content.
        # We don't want them to become MJML components directly, but rather
        # their children should populate mjml or mj-body.
        if node.name == 'html' or node.name == 'body':
            converted_children = []
            for child in node.children:
                # Recursively convert children. If a child returns a list, extend.
                result = convert_html_node_to_mjml(child)
                if result:
                    if isinstance(result, list):
                        converted_children.extend(result)
                    else:
                        converted_children.append(result)
            # Return the collected children to be processed by the caller (html_to_mjml_json)
            return converted_children

        elif node.name == 'table':
            mjml_element["tagName"] = "mj-section"
            mjml_element["attributes"]['full-width'] = 'full-width' if node.get('width') == '100%' or 'width' not in mjml_element["attributes"] else 'false'
            if 'background-color' in mjml_attrs:
                mjml_element["attributes"]['container-background-color'] = mjml_attrs.pop('background-color')
            if 'width' in mjml_attrs:
                mjml_element["attributes"]['width'] = mjml_attrs.pop('width')
            if 'padding' in mjml_attrs:
                mjml_element["attributes"]['padding'] = mjml_attrs.pop('padding')

            for row in node.find_all('tr', recursive=False):
                mjml_group = {"tagName": "mj-group", "attributes": {}, "children": []}
                row_styles = parse_inline_styles(row.get('style'))
                row_mjml_attrs, _ = map_styles_to_mjml_attrs(row_styles)
                mjml_group["attributes"].update(row_mjml_attrs)

                cells = row.find_all(['td', 'th'], recursive=False)
                if len(cells) == 1 and cells[0].find('table', recursive=False):
                    inner_table_mjml = convert_html_node_to_mjml(cells[0].find('table', recursive=False))
                    if inner_table_mjml:
                        if isinstance(inner_table_mjml, list): # handle if inner table returns list
                            mjml_element["children"].extend(inner_table_mjml)
                        else:
                            mjml_element["children"].append(inner_table_mjml)
                else:
                    for cell in cells:
                        mjml_column = {"tagName": "mj-column", "attributes": {}, "children": []}
                        cell_styles = parse_inline_styles(cell.get('style'))
                        cell_mjml_attrs, _ = map_styles_to_mjml_attrs(cell_styles)
                        mjml_column["attributes"].update(cell_mjml_attrs)

                        if cell.has_attr('width'):
                            mjml_column["attributes"]['width'] = cell['width'] + ('%' if '%' in cell['width'] else '')
                        elif 'width' in cell_mjml_attrs:
                             mjml_column["attributes"]['width'] = cell_mjml_attrs.pop('width')

                        if cell.has_attr('valign'):
                            mjml_column["attributes"]['vertical-align'] = cell['valign']
                        elif 'vertical-align' in cell_mjml_attrs:
                             mjml_column["attributes"]['vertical-align'] = cell_mjml_attrs.pop('vertical-align')

                        for cell_child in cell.children:
                            converted_cell_child = convert_html_node_to_mjml(cell_child)
                            if converted_cell_child:
                                if isinstance(converted_cell_child, list):
                                    mjml_column["children"].extend(converted_cell_child)
                                else:
                                    mjml_column["children"].append(converted_cell_child)
                        mjml_group["children"].append(mjml_column)
                    mjml_element["children"].append(mjml_group)

            if not mjml_element["children"]:
                return None
            return mjml_element

        elif node.name in ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'em', 'b', 'i', 'a']:
            mjml_element["tagName"] = "mj-text"
            content_html = ""
            for child in node.children:
                if isinstance(child, Tag):
                    content_html += str(child)
                else:
                    content_html += str(child).strip()

            if node.name == 'a' and node.get_text(strip=True).strip():
                # Heuristic for a button
                if 'background-color' in mjml_attrs and 'padding' in mjml_attrs: # Check for background and padding to infer button
                    mjml_element["tagName"] = "mj-button"
                    mjml_element['attributes']['href'] = node.get('href', '#')
                    mjml_element['attributes']['background-color'] = mjml_attrs.pop('background-color')
                    mjml_element['attributes']['color'] = mjml_attrs.pop('color', '#ffffff')
                    if 'padding' in mjml_attrs: mjml_element['attributes']['padding'] = mjml_attrs.pop('padding')
                    if 'font-size' in mjml_attrs: mjml_element['attributes']['font-size'] = mjml_attrs.pop('font-size')
                    if 'font-family' in mjml_attrs: mjml_element['attributes']['font-family'] = mjml_attrs.pop('font-family')
                    if 'border-radius' in mjml_attrs: mjml_element['attributes']['border-radius'] = mjml_attrs.pop('border-radius')
                    if 'text-align' in mjml_attrs: mjml_element['attributes']['align'] = mjml_attrs.pop('text-align')
                    mjml_element['content'] = node.get_text(strip=True)
                    mjml_element["children"] = [] # No children for mj-button content
                else:
                    mjml_element["content"] = content_html
            else:
                mjml_element["content"] = content_html

            if not mjml_element.get("content", "").strip() and not mjml_element["children"] and mjml_element.get("tagName") != "mj-button":
                return None
            return mjml_element

        elif node.name == 'img':
            mjml_element["tagName"] = "mj-image"
            mjml_element['attributes']['src'] = node.get('src')
            mjml_element['attributes']['alt'] = node.get('alt', '')
            if node.has_attr('width'):
                mjml_element['attributes']['width'] = node['width']
            if node.has_attr('height'):
                mjml_element['attributes']['height'] = node['height']
            if 'text-align' in mjml_attrs:
                mjml_element['attributes']['align'] = mjml_attrs.pop('text-align')
            elif node.has_attr('align'):
                mjml_element['attributes']['align'] = node['align']

            if 'background-color' in mjml_attrs:
                 mjml_element["attributes"]['container-background-color'] = mjml_attrs.pop('background-color')
            return mjml_element

        elif node.name in ['head', 'style', 'script', 'link', 'meta', 'title']:
            return None

        elif node.name in ['ul', 'ol', 'li']:
             mjml_element["tagName"] = "mj-text"
             mjml_element["content"] = str(node)
             return mjml_element

        # Fallback for unhandled block-level elements
        else:
            if node.get_text(strip=True).strip() or node.find(True): # Has content or children
                # Try to wrap it in a section and column
                mjml_element["tagName"] = "mj-section"
                if 'background-color' in mjml_attrs:
                    mjml_element["attributes"]['container-background-color'] = mjml_attrs.pop('background-color')
                if 'padding' in mjml_attrs:
                    mjml_element["attributes"]['padding'] = mjml_attrs.pop('padding')

                mjml_column = {"tagName": "mj-column", "attributes": {}, "children": []}
                mjml_element["children"].append(mjml_column)

                # Process children for the column
                for child in node.children:
                    converted_child = convert_html_node_to_mjml(child)
                    if converted_child:
                        if isinstance(converted_child, list):
                            mjml_column["children"].extend(converted_child)
                        else:
                            mjml_column["children"].append(converted_child)
                if not mjml_column["children"]:
                    return None
                return mjml_element

        return None

    elif isinstance(node, str) and node.strip():
        # Handle raw text nodes that are not inside a specific tag
        return {"tagName": "mj-text", "content": node.strip(), "attributes": {}}
    return None


def mjml_dict_to_xml(mjml_dict, indent_level=0):
    """
    Converts a MJML dictionary structure to XML/MJML markup string.
    """
    indent = "  " * indent_level
    tag_name = mjml_dict.get("tagName", "")
    attributes = mjml_dict.get("attributes", {})
    children = mjml_dict.get("children", [])
    content = mjml_dict.get("content", "")

    # Build attributes string
    attr_str = ""
    if attributes:
        attr_parts = []
        for key, value in attributes.items():
            attr_parts.append(f'{key}="{value}"')
        attr_str = " " + " ".join(attr_parts)

    # Handle self-closing tags or tags with content
    if content and not children:
        return f"{indent}<{tag_name}{attr_str}>{content}</{tag_name}>"
    elif not children and not content:
        return f"{indent}<{tag_name}{attr_str} />"
    else:
        # Tag with children
        result = f"{indent}<{tag_name}{attr_str}>"
        if content:
            result += content
        if children:
            result += "\n"
            for child in children:
                result += mjml_dict_to_xml(child, indent_level + 1) + "\n"
            result += indent
        result += f"</{tag_name}>"
        return result


def html_to_mjml_xml(html_content):
    """
    Converts a given HTML string into MJML markup,
    aiming for high fidelity in content, styles, and attributes.
    """
    soup = BeautifulSoup(html_content, 'lxml')

    # Initialize the mj-body children list
    mj_body_children = []

    # Get the root HTML tag or the body tag
    root_html_or_body = soup.find('html') or soup.find('body')

    if root_html_or_body:
        # Pass the root_html_or_body to the converter.
        # It will return a list of top-level sections/wrappers.
        converted_content = convert_html_node_to_mjml(root_html_or_body)
        if isinstance(converted_content, list):
            mj_body_children.extend(converted_content)
        elif converted_content:
            mj_body_children.append(converted_content)
    else:
        # Fallback if neither html nor body tag is found (e.g., HTML fragment)
        for child in soup.children:
            if isinstance(child, Tag) and child.name not in ['head', 'style', 'script', 'link', 'meta', 'title']:
                converted = convert_html_node_to_mjml(child)
                if converted:
                    if isinstance(converted, list):
                        mj_body_children.extend(converted)
                    else:
                        mj_body_children.append(converted)
            elif isinstance(child, str) and child.strip():
                converted = convert_html_node_to_mjml(child)
                if converted:
                    mj_body_children.append(converted)

    # Construct the final MJML structure
    final_mjml_dict = {
        "tagName": "mjml",
        "attributes": {},
        "children": [
            {
                "tagName": "mj-body",
                "attributes": {},
                "children": mj_body_children
            }
        ]
    }

    # Clean up empty children arrays and None values
    def clean_empty_children(obj):
        if isinstance(obj, dict):
            # Check for "children" key specifically and remove if empty
            if "children" in obj and not obj["children"]:
                del obj["children"]
            # Recurse for other keys
            for key, value in obj.items():
                obj[key] = clean_empty_children(value)
        elif isinstance(obj, list):
            # Filter out None values and recurse for list elements
            obj = [clean_empty_children(elem) for elem in obj if elem is not None]
        return obj

    final_mjml_dict = clean_empty_children(final_mjml_dict)

    # Convert to XML markup
    return mjml_dict_to_xml(final_mjml_dict)

# --- How to use it ---
if __name__ == "__main__":
    example_html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <style>
            .button-style { background-color: #007bff; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; display: inline-block;}
        </style>
    </head>
    <body>
        <table width="600" border="0" cellpadding="0" cellspacing="0" align="center" style="background-color:#ffffff; border:1px solid #e0e0e0; padding:20px;">
            <tr>
                <td align="center" style="padding-bottom: 20px;">
                    <img src="https://via.placeholder.com/150x50" alt="Company Logo" width="150" height="50" style="display:block; border:0;">
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 20px;">
                    <h1 style="font-family: Arial, sans-serif; font-size: 24px; color: #333333; text-align: center;">Welcome to Our Newsletter!</h1>
                </td>
            </tr>
            <tr>
                <td style="padding-left: 20px; padding-right: 20px; color:#555555; font-family: 'Open Sans', Arial, sans-serif;">
                    <p style="font-size: 16px; line-height: 24px;">
                        Dear Subscriber,
                    </p>
                    <p style="font-size: 16px; line-height: 24px;">
                        Thank you for signing up for our newsletter. We are excited to share the latest updates and offers with you.
                    </p>
                    <div style="text-align: center; margin-top: 30px; margin-bottom: 30px;">
                        <a href="https://example.com/learn-more" style="background-color: #008CBA; color: white; padding: 12px 25px; text-align: center; text-decoration: none; display: inline-block; font-size: 16px; border-radius: 8px;">
                            Learn More
                        </a>
                    </div>
                    <ul>
                        <li>Feature One: <span style="font-weight: bold;">Exciting new stuff.</span></li>
                        <li>Feature Two: More exciting stuff.</li>
                    </ul>
                </td>
            </tr>
            <tr>
                <td align="center" style="padding-top: 20px; color:#999999; font-size:12px; font-family: Arial, sans-serif;">
                    <p>&copy; 2023 Your Company. All rights reserved.</p>
                    <p><a href="https://example.com/unsubscribe" style="color:#999999;">Unsubscribe</a> | <a href="https://example.com/privacy" style="color:#999999;">Privacy Policy</a></p>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """

    mjml_xml_output = html_to_mjml_xml(example_html_template)
    print(mjml_xml_output)

    output_filename = "test.mjml"
    with open(output_filename, "w", encoding="utf-8") as f:
        f.write(mjml_xml_output)
    print(f"\nMJML markup saved to {output_filename}. You can now run:")
    print(f"mjml {output_filename} -o test2.html")