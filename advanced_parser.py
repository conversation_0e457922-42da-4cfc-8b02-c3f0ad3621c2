#!/usr/bin/env python3
"""
Advanced HTML to MJML parser that handles complex email structures
with multiple embedded HTML documents and nested tables.
"""

from bs4 import BeautifulSoup, Tag, NavigableString
import re
import sys

def clean_html_content(html_content):
    """
    Clean and preprocess HTML content to handle embedded documents.
    """
    # Remove multiple DOCTYPE declarations except the first one
    doctype_pattern = r'<!DOCTYPE[^>]*>'
    doctypes = re.findall(doctype_pattern, html_content, re.IGNORECASE)
    
    if len(doctypes) > 1:
        # Keep only the first DOCTYPE and remove the rest
        first_doctype = doctypes[0]
        # Remove all DOCTYPEs
        html_content = re.sub(doctype_pattern, '', html_content, flags=re.IGNORECASE)
        # Add back the first one at the beginning
        html_content = first_doctype + '\n' + html_content
    
    # Remove embedded <html> and <head> tags that are not at the root level
    # This is a complex regex to handle nested HTML documents
    html_content = re.sub(r'<html[^>]*>(?=.*<html)', '', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r'</html>(?=.*<html)', '', html_content, flags=re.IGNORECASE)
    
    return html_content

def extract_meaningful_content(soup):
    """
    Extract meaningful content from the complex nested structure.
    Focus on actual email content rather than structural elements.
    """
    content_elements = []

    # Look for ALL images first - they're crucial for email design
    for img in soup.find_all('img'):
        src = img.get('src')
        if src and 'image.email.ally.com' in src:  # Focus on actual content images
            content_elements.append(img)

    # Look for elements with background colors (sections)
    for element in soup.find_all(['table', 'td', 'div']):
        style = element.get('style', '')
        bgcolor = element.get('bgcolor', '')

        # Check for meaningful background colors
        if ('background-color:' in style and any(color in style for color in ['#300942', '#650360', '#FFF7F0', '#FFFFFF'])) or \
           bgcolor in ['#300942', '#650360', '#FFF7F0', '#ffffff']:
            # This is likely a section container
            content_elements.append(element)

    # Look for text content with meaningful length
    for element in soup.find_all(['td', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        text = element.get_text(strip=True)
        if len(text) > 15 and not is_structural_element(element):
            # Check if this text isn't already captured in a background section
            already_captured = False
            for captured in content_elements:
                if element in captured.find_all() if hasattr(captured, 'find_all') else False:
                    already_captured = True
                    break
            if not already_captured:
                content_elements.append(element)

    # Look for links/buttons
    for link in soup.find_all('a'):
        href = link.get('href')
        if href and (link.get_text(strip=True) or link.find('img')):
            content_elements.append(link)

    return content_elements

def is_structural_element(element):
    """
    Determine if an element is structural (like spacers) rather than content.
    """
    text = element.get_text(strip=True)

    # Check for spacer patterns
    if text in ['&nbsp;', ' ', ''] or text.isspace():
        return True

    # Check for CSS/style content
    if element.name in ['style', 'script', 'head', 'meta', 'link']:
        return True

    # Check for Outlook conditional comments
    if '<!--[if' in str(element) or '<![endif]' in str(element):
        return True

    return False

def get_section_background(element):
    """
    Determine the background color for a section based on the element's context.
    """
    # Check the element itself and its parents for background colors
    current = element
    for _ in range(5):  # Check up to 5 levels up
        if current is None:
            break

        # Check style attribute
        style = current.get('style', '') if hasattr(current, 'get') else ''
        if 'background-color:' in style:
            bg_match = re.search(r'background-color:\s*([^;]+)', style)
            if bg_match:
                color = bg_match.group(1).strip()
                if color in ['#300942', '#650360', '#FFF7F0', '#FFFFFF']:
                    return color

        # Check bgcolor attribute
        bgcolor = current.get('bgcolor', '') if hasattr(current, 'get') else ''
        if bgcolor in ['#300942', '#650360', '#FFF7F0', '#ffffff']:
            return bgcolor.upper() if bgcolor == '#ffffff' else bgcolor

        current = current.parent if hasattr(current, 'parent') else None

    return None

def has_background_color(element):
    """
    Check if an element has a meaningful background color.
    """
    style = element.get('style', '') if hasattr(element, 'get') else ''
    bgcolor = element.get('bgcolor', '') if hasattr(element, 'get') else ''

    meaningful_colors = ['#300942', '#650360', '#FFF7F0', '#FFFFFF', '#ffffff']

    if 'background-color:' in style:
        for color in meaningful_colors:
            if color.lower() in style.lower():
                return True

    if bgcolor in meaningful_colors or bgcolor.lower() in [c.lower() for c in meaningful_colors]:
        return True

    return False

def extract_text_styles(style_string):
    """
    Extract text styling attributes from CSS style string.
    """
    attributes = {}

    if not style_string:
        return attributes

    # Parse common text styles
    style_mappings = {
        'color': 'color',
        'font-size': 'font-size',
        'font-family': 'font-family',
        'font-weight': 'font-weight',
        'text-align': 'align',
        'line-height': 'line-height',
        'letter-spacing': 'letter-spacing',
        'text-decoration': 'text-decoration'
    }

    for css_prop, mjml_attr in style_mappings.items():
        pattern = rf'{css_prop}:\s*([^;]+)'
        match = re.search(pattern, style_string)
        if match:
            value = match.group(1).strip()
            if value:
                attributes[mjml_attr] = value

    return attributes

def create_mjml_structure(content_elements):
    """
    Create a clean MJML structure from extracted content elements.
    """
    mjml_sections = []
    processed_elements = set()

    for element in content_elements:
        if id(element) in processed_elements:
            continue

        if element.name == 'img':
            # Create image section
            src = element.get('src', '')
            alt = element.get('alt', '')
            width = element.get('width', '')
            height = element.get('height', '')

            # Determine section background based on parent elements
            section_bg = get_section_background(element)

            mjml_element = {
                "tagName": "mj-section",
                "attributes": {},
                "children": [{
                    "tagName": "mj-column",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-image",
                        "attributes": {
                            "src": src,
                            "alt": alt,
                        }
                    }]
                }]
            }

            # Add section background
            if section_bg:
                mjml_element["attributes"]["background-color"] = section_bg

            # Add image dimensions
            if width:
                mjml_element["children"][0]["children"][0]["attributes"]["width"] = width + ('px' if width.isdigit() else '')
            if height:
                mjml_element["children"][0]["children"][0]["attributes"]["height"] = height + ('px' if height.isdigit() else '')

            mjml_sections.append(mjml_element)
            processed_elements.add(id(element))

        elif element.name == 'table' and has_background_color(element):
            # This is likely a section container - extract its content
            section_bg = get_section_background(element)
            section_content = []

            # Look for images within this table
            for img in element.find_all('img'):
                if img.get('src'):
                    img_mjml = {
                        "tagName": "mj-image",
                        "attributes": {
                            "src": img.get('src', ''),
                            "alt": img.get('alt', ''),
                        }
                    }
                    if img.get('width'):
                        img_mjml["attributes"]["width"] = img['width'] + ('px' if img['width'].isdigit() else '')
                    section_content.append(img_mjml)

            # Look for text content within this table
            for text_elem in element.find_all(['td', 'div', 'p', 'h1', 'h2', 'h3']):
                text = text_elem.get_text(strip=True)
                if len(text) > 15 and not is_structural_element(text_elem):
                    # Extract styles
                    style = text_elem.get('style', '')
                    attributes = extract_text_styles(style)

                    text_mjml = {
                        "tagName": "mj-text",
                        "attributes": attributes,
                        "content": text
                    }
                    section_content.append(text_mjml)

            # Create section if we found content
            if section_content:
                mjml_element = {
                    "tagName": "mj-section",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-column",
                        "attributes": {},
                        "children": section_content
                    }]
                }

                if section_bg:
                    mjml_element["attributes"]["background-color"] = section_bg

                mjml_sections.append(mjml_element)
                processed_elements.add(id(element))

        elif element.name == 'a' and element.get('href'):
            # Create button/link section
            href = element.get('href', '#')

            # Check if link contains an image
            img = element.find('img')
            if img and img.get('src'):
                mjml_element = {
                    "tagName": "mj-section",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-column",
                        "attributes": {},
                        "children": [{
                            "tagName": "mj-image",
                            "attributes": {
                                "src": img.get('src', ''),
                                "alt": img.get('alt', ''),
                                "href": href
                            }
                        }]
                    }]
                }
                if img.get('width'):
                    mjml_element["children"][0]["children"][0]["attributes"]["width"] = img['width'] + ('px' if img['width'].isdigit() else '')
            else:
                # Text link
                text_content = element.get_text(strip=True)
                if text_content:
                    # Check if it looks like a button
                    style = element.get('style', '')
                    is_button = 'background-color' in style or 'padding' in style

                    if is_button:
                        mjml_element = {
                            "tagName": "mj-section",
                            "attributes": {},
                            "children": [{
                                "tagName": "mj-column",
                                "attributes": {},
                                "children": [{
                                    "tagName": "mj-button",
                                    "attributes": {
                                        "href": href
                                    },
                                    "content": text_content
                                }]
                            }]
                        }
                    else:
                        mjml_element = {
                            "tagName": "mj-section",
                            "attributes": {},
                            "children": [{
                                "tagName": "mj-column",
                                "attributes": {},
                                "children": [{
                                    "tagName": "mj-text",
                                    "attributes": {},
                                    "content": f'<a href="{href}">{text_content}</a>'
                                }]
                            }]
                        }
                else:
                    continue

            mjml_sections.append(mjml_element)
            processed_elements.add(id(element))

        else:
            # Handle remaining text elements
            if hasattr(element, 'get_text'):
                text_content = element.get_text(strip=True)
                if text_content and len(text_content) > 10:
                    # Extract styles
                    style = element.get('style', '') if hasattr(element, 'get') else ''
                    attributes = extract_text_styles(style)

                    # Determine section background
                    section_bg = get_section_background(element)

                    mjml_element = {
                        "tagName": "mj-section",
                        "attributes": {},
                        "children": [{
                            "tagName": "mj-column",
                            "attributes": {},
                            "children": [{
                                "tagName": "mj-text",
                                "attributes": attributes,
                                "content": text_content
                            }]
                        }]
                    }

                    if section_bg:
                        mjml_element["attributes"]["background-color"] = section_bg

                    mjml_sections.append(mjml_element)
                    processed_elements.add(id(element))

    return mjml_sections

def mjml_dict_to_xml(mjml_dict, indent_level=0):
    """
    Convert MJML dictionary to XML markup.
    """
    indent = "  " * indent_level
    tag_name = mjml_dict.get("tagName", "")
    attributes = mjml_dict.get("attributes", {})
    children = mjml_dict.get("children", [])
    content = mjml_dict.get("content", "")
    
    # Build attributes string
    attr_str = ""
    if attributes:
        attr_parts = []
        for key, value in attributes.items():
            if value:  # Only add non-empty attributes
                attr_parts.append(f'{key}="{value}"')
        if attr_parts:
            attr_str = " " + " ".join(attr_parts)
    
    # Handle self-closing tags or tags with content
    if content and not children:
        return f"{indent}<{tag_name}{attr_str}>{content}</{tag_name}>"
    elif not children and not content:
        return f"{indent}<{tag_name}{attr_str} />"
    else:
        # Tag with children
        result = f"{indent}<{tag_name}{attr_str}>"
        if content:
            result += content
        if children:
            result += "\n"
            for child in children:
                result += mjml_dict_to_xml(child, indent_level + 1) + "\n"
            result += indent
        result += f"</{tag_name}>"
        return result

def advanced_html_to_mjml(html_content):
    """
    Advanced conversion from complex HTML to clean MJML.
    """
    # Clean the HTML content
    cleaned_html = clean_html_content(html_content)
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(cleaned_html, 'html.parser')
    
    # Extract meaningful content
    content_elements = extract_meaningful_content(soup)
    
    # Remove duplicates based on text content
    unique_elements = []
    seen_texts = set()
    
    for element in content_elements:
        text = element.get_text(strip=True)
        if text not in seen_texts:
            seen_texts.add(text)
            unique_elements.append(element)
    
    # Create MJML structure
    mjml_sections = create_mjml_structure(unique_elements)
    
    # Build final MJML document
    mjml_document = {
        "tagName": "mjml",
        "attributes": {},
        "children": [{
            "tagName": "mj-body",
            "attributes": {},
            "children": mjml_sections
        }]
    }
    
    return mjml_dict_to_xml(mjml_document)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python advanced_parser.py <html_file>")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        mjml_output = advanced_html_to_mjml(html_content)
        
        # Output to .mjml file
        base_name = html_file.replace('.html', '')
        output_file = f"{base_name}_advanced.mjml"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mjml_output)
        
        print(f"✅ Advanced conversion completed!")
        print(f"📧 Input: {html_file}")
        print(f"📧 Output: {output_file}")
        print(f"🚀 Run: mjml {output_file} -o {base_name}_final.html")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
