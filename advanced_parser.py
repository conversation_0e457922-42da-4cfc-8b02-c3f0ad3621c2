#!/usr/bin/env python3
"""
Advanced HTML to MJML parser that handles complex email structures
with multiple embedded HTML documents and nested tables.
"""

from bs4 import BeautifulSoup, Tag, NavigableString
import re
import sys

def clean_html_content(html_content):
    """
    Clean and preprocess HTML content to handle embedded documents.
    """
    # Remove multiple DOCTYPE declarations except the first one
    doctype_pattern = r'<!DOCTYPE[^>]*>'
    doctypes = re.findall(doctype_pattern, html_content, re.IGNORECASE)
    
    if len(doctypes) > 1:
        # Keep only the first DOCTYPE and remove the rest
        first_doctype = doctypes[0]
        # Remove all DOCTYPEs
        html_content = re.sub(doctype_pattern, '', html_content, flags=re.IGNORECASE)
        # Add back the first one at the beginning
        html_content = first_doctype + '\n' + html_content
    
    # Remove embedded <html> and <head> tags that are not at the root level
    # This is a complex regex to handle nested HTML documents
    html_content = re.sub(r'<html[^>]*>(?=.*<html)', '', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r'</html>(?=.*<html)', '', html_content, flags=re.IGNORECASE)
    
    return html_content

def extract_meaningful_content(soup):
    """
    Extract meaningful content from the complex nested structure.
    Focus on actual email content rather than structural elements.
    """
    content_elements = []
    
    # Look for text content with meaningful length
    for element in soup.find_all(['td', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        text = element.get_text(strip=True)
        if len(text) > 20 and not is_structural_element(element):
            content_elements.append(element)
    
    # Look for images
    for img in soup.find_all('img'):
        if img.get('src') and not is_structural_element(img):
            content_elements.append(img)
    
    # Look for links/buttons
    for link in soup.find_all('a'):
        if link.get('href') and link.get_text(strip=True):
            content_elements.append(link)
    
    return content_elements

def is_structural_element(element):
    """
    Determine if an element is structural (like spacers) rather than content.
    """
    text = element.get_text(strip=True)
    
    # Check for spacer patterns
    if text in ['&nbsp;', ' ', ''] or text.isspace():
        return True
    
    # Check for CSS/style content
    if element.name in ['style', 'script', 'head', 'meta', 'link']:
        return True
    
    # Check for Outlook conditional comments
    if '<!--[if' in str(element) or '<![endif]' in str(element):
        return True
    
    return False

def create_mjml_structure(content_elements):
    """
    Create a clean MJML structure from extracted content elements.
    """
    mjml_sections = []
    current_section = None
    
    for element in content_elements:
        if element.name == 'img':
            # Create image section
            mjml_element = {
                "tagName": "mj-section",
                "attributes": {},
                "children": [{
                    "tagName": "mj-column",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-image",
                        "attributes": {
                            "src": element.get('src', ''),
                            "alt": element.get('alt', ''),
                        }
                    }]
                }]
            }
            
            # Add width and height if present
            if element.get('width'):
                mjml_element["children"][0]["children"][0]["attributes"]["width"] = element['width'] + 'px'
            if element.get('height'):
                mjml_element["children"][0]["children"][0]["attributes"]["height"] = element['height'] + 'px'
                
            mjml_sections.append(mjml_element)
            
        elif element.name == 'a' and element.get('href'):
            # Create button/link section
            text_content = element.get_text(strip=True)
            
            # Check if it looks like a button (has background color or padding)
            style = element.get('style', '')
            is_button = 'background-color' in style or 'padding' in style
            
            if is_button:
                mjml_element = {
                    "tagName": "mj-section",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-column",
                        "attributes": {},
                        "children": [{
                            "tagName": "mj-button",
                            "attributes": {
                                "href": element.get('href', '#')
                            },
                            "content": text_content
                        }]
                    }]
                }
            else:
                mjml_element = {
                    "tagName": "mj-section",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-column",
                        "attributes": {},
                        "children": [{
                            "tagName": "mj-text",
                            "attributes": {},
                            "content": f'<a href="{element.get("href", "#")}">{text_content}</a>'
                        }]
                    }]
                }
            
            mjml_sections.append(mjml_element)
            
        else:
            # Create text section
            text_content = element.get_text(strip=True)
            if text_content:
                # Extract styles
                style = element.get('style', '')
                attributes = {}
                
                # Parse common styles
                if 'color:' in style:
                    color_match = re.search(r'color:\s*([^;]+)', style)
                    if color_match:
                        attributes['color'] = color_match.group(1).strip()
                
                if 'font-size:' in style:
                    size_match = re.search(r'font-size:\s*([^;]+)', style)
                    if size_match:
                        attributes['font-size'] = size_match.group(1).strip()
                
                if 'font-family:' in style:
                    family_match = re.search(r'font-family:\s*([^;]+)', style)
                    if family_match:
                        attributes['font-family'] = family_match.group(1).strip()
                
                if 'text-align:' in style:
                    align_match = re.search(r'text-align:\s*([^;]+)', style)
                    if align_match:
                        attributes['align'] = align_match.group(1).strip()
                
                mjml_element = {
                    "tagName": "mj-section",
                    "attributes": {},
                    "children": [{
                        "tagName": "mj-column",
                        "attributes": {},
                        "children": [{
                            "tagName": "mj-text",
                            "attributes": attributes,
                            "content": text_content
                        }]
                    }]
                }
                
                mjml_sections.append(mjml_element)
    
    return mjml_sections

def mjml_dict_to_xml(mjml_dict, indent_level=0):
    """
    Convert MJML dictionary to XML markup.
    """
    indent = "  " * indent_level
    tag_name = mjml_dict.get("tagName", "")
    attributes = mjml_dict.get("attributes", {})
    children = mjml_dict.get("children", [])
    content = mjml_dict.get("content", "")
    
    # Build attributes string
    attr_str = ""
    if attributes:
        attr_parts = []
        for key, value in attributes.items():
            if value:  # Only add non-empty attributes
                attr_parts.append(f'{key}="{value}"')
        if attr_parts:
            attr_str = " " + " ".join(attr_parts)
    
    # Handle self-closing tags or tags with content
    if content and not children:
        return f"{indent}<{tag_name}{attr_str}>{content}</{tag_name}>"
    elif not children and not content:
        return f"{indent}<{tag_name}{attr_str} />"
    else:
        # Tag with children
        result = f"{indent}<{tag_name}{attr_str}>"
        if content:
            result += content
        if children:
            result += "\n"
            for child in children:
                result += mjml_dict_to_xml(child, indent_level + 1) + "\n"
            result += indent
        result += f"</{tag_name}>"
        return result

def advanced_html_to_mjml(html_content):
    """
    Advanced conversion from complex HTML to clean MJML.
    """
    # Clean the HTML content
    cleaned_html = clean_html_content(html_content)
    
    # Parse with BeautifulSoup
    soup = BeautifulSoup(cleaned_html, 'html.parser')
    
    # Extract meaningful content
    content_elements = extract_meaningful_content(soup)
    
    # Remove duplicates based on text content
    unique_elements = []
    seen_texts = set()
    
    for element in content_elements:
        text = element.get_text(strip=True)
        if text not in seen_texts:
            seen_texts.add(text)
            unique_elements.append(element)
    
    # Create MJML structure
    mjml_sections = create_mjml_structure(unique_elements)
    
    # Build final MJML document
    mjml_document = {
        "tagName": "mjml",
        "attributes": {},
        "children": [{
            "tagName": "mj-body",
            "attributes": {},
            "children": mjml_sections
        }]
    }
    
    return mjml_dict_to_xml(mjml_document)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python advanced_parser.py <html_file>")
        sys.exit(1)
    
    html_file = sys.argv[1]
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        mjml_output = advanced_html_to_mjml(html_content)
        
        # Output to .mjml file
        base_name = html_file.replace('.html', '')
        output_file = f"{base_name}_advanced.mjml"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(mjml_output)
        
        print(f"✅ Advanced conversion completed!")
        print(f"📧 Input: {html_file}")
        print(f"📧 Output: {output_file}")
        print(f"🚀 Run: mjml {output_file} -o {base_name}_final.html")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
