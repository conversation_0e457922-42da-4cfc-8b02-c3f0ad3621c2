#!/usr/bin/env python3
"""
Script to convert MJML JSON format to MJML markup format.
This fixes the issue where MJML CLI expects markup, not JSON.
"""

import json
import sys

def mjml_dict_to_xml(mjml_dict, indent_level=0):
    """
    Converts a MJML dictionary structure to XML/MJML markup string.
    """
    indent = "  " * indent_level
    tag_name = mjml_dict.get("tagName", "")
    attributes = mjml_dict.get("attributes", {})
    children = mjml_dict.get("children", [])
    content = mjml_dict.get("content", "")
    
    # Build attributes string
    attr_str = ""
    if attributes:
        attr_parts = []
        for key, value in attributes.items():
            attr_parts.append(f'{key}="{value}"')
        attr_str = " " + " ".join(attr_parts)
    
    # Handle self-closing tags or tags with content
    if content and not children:
        return f"{indent}<{tag_name}{attr_str}>{content}</{tag_name}>"
    elif not children and not content:
        return f"{indent}<{tag_name}{attr_str} />"
    else:
        # Tag with children
        result = f"{indent}<{tag_name}{attr_str}>"
        if content:
            result += content
        if children:
            result += "\n"
            for child in children:
                result += mjml_dict_to_xml(child, indent_level + 1) + "\n"
            result += indent
        result += f"</{tag_name}>"
        return result

def convert_json_to_mjml(json_file_path):
    """
    Convert JSON file to MJML markup.
    """
    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Handle both formats: {"mjml": {...}} and direct {...}
    if "mjml" in data:
        mjml_dict = data["mjml"]
    else:
        mjml_dict = data
    
    return mjml_dict_to_xml(mjml_dict)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python json_to_mjml.py <json_file>")
        sys.exit(1)
    
    json_file = sys.argv[1]
    mjml_output = convert_json_to_mjml(json_file)
    
    # Output to .mjml file
    output_file = json_file.replace('.json', '.mjml')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(mjml_output)
    
    print(f"Converted {json_file} to {output_file}")
    print(f"You can now run: mjml {output_file} -o output.html")
